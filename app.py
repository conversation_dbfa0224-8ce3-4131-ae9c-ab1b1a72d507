
import cv2
import time
import threading
import queue
import uuid
import numpy as np
from flask import Flask, Response, render_template, request, redirect, url_for, jsonify, session, flash
import os
from ultralytics import YOLO  # Add YOLO import

# Set environment variables to suppress HEVC/FFmpeg warnings and errors
os.environ['OPENCV_FFMPEG_LOGLEVEL'] = '-8'  # Suppress FFmpeg logs
os.environ['FFMPEG_LOG_LEVEL'] = 'quiet'     # Quiet FFmpeg output
from datetime import datetime, timedelta
import base64
import av
import json
from collections import defaultdict
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import yagmail
from email.mime.image import MIMEImage
from functools import wraps

app = Flask(__name__, static_folder='static', template_folder='templates')
app.secret_key = 'your-secret-key-change-this-in-production'  # Change this in production

# Login credentials
LOGIN_USERNAME = 'admin'
LOGIN_PASSWORD = 'password123'

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# Add a template filter for base64 encoding
@app.template_filter('b64encode')
def b64encode_filter(data):
    return base64.b64encode(data).decode('utf-8')

# Create a directory to store detection events
if not os.path.exists('detection_events'):
    os.makedirs('detection_events')

# Create a directory to store historical data
if not os.path.exists('history_data'):
    os.makedirs('history_data')

# Create a directory to store camera data
if not os.path.exists('camera_data'):
    os.makedirs('camera_data')

# Store cameras in a dictionary with unique IDs
cameras = {}

# Global variables for improved detection
detection_history = {}  # Track detection history for temporal consistency
digest_data = {}  # Store digest data for notifications

# Functions for camera persistence
def save_cameras():
    """Save camera configurations to JSON file"""
    cameras_file = 'camera_data/cameras.json'

    # Prepare camera data for saving (exclude non-serializable objects)
    camera_data = {}
    for camera_id, camera in cameras.items():
        # Convert daily_minute_presence sets to lists for JSON serialization
        daily_minute_presence = {}
        if 'daily_minute_presence' in camera:
            for date, minute_set in camera['daily_minute_presence'].items():
                daily_minute_presence[date] = list(minute_set) if isinstance(minute_set, set) else minute_set

        camera_data[camera_id] = {
            'name': camera['name'],
            'url': camera['url'],
            'detection_status': camera['detection_status'],
            'notification_status': camera['notification_status'],
            'hourly_stats': camera['hourly_stats'],
            'minute_presence': list(camera['minute_presence']) if isinstance(camera['minute_presence'], set) else camera['minute_presence'],
            'daily_minute_presence': daily_minute_presence
        }

    try:
        with open(cameras_file, 'w') as f:
            json.dump(camera_data, f, indent=4)
        print(f"Cameras saved to {cameras_file}")
    except Exception as e:
        print(f"Error saving cameras: {e}")

def load_cameras():
    """Load camera configurations from JSON file"""
    cameras_file = 'camera_data/cameras.json'

    if os.path.exists(cameras_file):
        try:
            with open(cameras_file, 'r') as f:
                camera_data = json.load(f)

            for camera_id, camera_config in camera_data.items():
                cameras[camera_id] = {
                    'name': camera_config['name'],
                    'url': camera_config['url'],
                    'queue': queue.Queue(maxsize=2),  # Reduced queue size to minimize lag
                    'running': True,
                    'detection_status': camera_config.get('detection_status', True),
                    'notification_status': camera_config.get('notification_status', False),
                    'detection_events': [],
                    'hourly_stats': camera_config.get('hourly_stats', {}),
                    'minute_presence': set(camera_config.get('minute_presence', [])),
                    'daily_minute_presence': {
                        date: set(minute_list) if isinstance(minute_list, list) else minute_list
                        for date, minute_list in camera_config.get('daily_minute_presence', {}).items()
                    },
                    'last_detection_second': None
                }

                # Start capture thread for this camera
                thread = threading.Thread(target=capture_frames, args=(camera_id, camera_config['url']))
                thread.daemon = True
                thread.start()

            print(f"Loaded {len(camera_data)} cameras from {cameras_file}")
        except Exception as e:
            print(f"Error loading cameras: {e}")
    else:
        print("No saved cameras found")

# Load YOLO model instead of MobileNetSSD
print("Loading YOLOv8n model...")
try:
    # Use YOLOv8n - the smallest and fastest YOLO model
    yolo_model = YOLO('yolov8n.pt')
    print("YOLO model loaded successfully!")
except Exception as e:
    print(f"Error loading YOLO model: {e}")
    yolo_model = None

# MobileNet SSD class names
CLASSES = ["background", "aeroplane", "bicycle", "bird", "boat",
           "bottle", "bus", "car", "cat", "chair", "cow", "diningtable",
           "dog", "horse", "motorbike", "person", "pottedplant", "sheep",
           "sofa", "train", "tvmonitor"]

def calculate_iou(box1, box2):
    """Calculate Intersection over Union (IoU) of two bounding boxes."""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # Calculate intersection area
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)

    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0

    intersection_area = (x2_i - x1_i) * (y2_i - y1_i)

    # Calculate union area
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - intersection_area

    if union_area == 0:
        return 0.0

    return intersection_area / union_area

def cleanup_old_data():
    """Clean up old data and prepare for new day"""
    current_date = datetime.now().strftime("%Y-%m-%d")

    for camera_id, camera in cameras.items():
        # Initialize daily_minute_presence if not exists
        if 'daily_minute_presence' not in camera:
            camera['daily_minute_presence'] = {}

        # Keep only last 30 days of data to prevent excessive storage
        dates_to_keep = []
        for i in range(30):
            date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            dates_to_keep.append(date)

        # Remove old dates from daily_minute_presence
        dates_to_remove = []
        for date in camera['daily_minute_presence'].keys():
            if date not in dates_to_keep:
                dates_to_remove.append(date)

        for date in dates_to_remove:
            del camera['daily_minute_presence'][date]

        # Save historical data for yesterday before cleanup
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        if yesterday in camera['daily_minute_presence']:
            save_historical_data(camera_id, yesterday)

    # Save updated camera data
    save_cameras()
    print(f"Data cleanup completed for {current_date}")

def schedule_midnight_cleanup():
    """Schedule the next midnight cleanup"""
    now = datetime.now()
    # Calculate time until next midnight
    tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
    seconds_until_midnight = (tomorrow - now).total_seconds()

    # Schedule cleanup at midnight
    cleanup_timer = threading.Timer(seconds_until_midnight, midnight_cleanup)
    cleanup_timer.daemon = True
    cleanup_timer.start()

    print(f"Scheduled midnight cleanup in {seconds_until_midnight/3600:.2f} hours")

def midnight_cleanup():
    """Perform midnight cleanup and schedule next one"""
    cleanup_old_data()
    schedule_midnight_cleanup()  # Schedule next cleanup

def detect_humans(frame, camera_id):
    # If model isn't loaded, return the frame as is with no detections
    if yolo_model is None:
        return frame, 0

    try:
        # Run YOLOv8 inference on the frame
        results = yolo_model(frame, stream=True, classes=0)  # class 0 is person in COCO dataset
        
        # Initialize person count
        person_count = 0
        person_detected = False
        
        # Process results
        for r in results:
            boxes = r.boxes
            for box in boxes:
                # Get confidence
                confidence = float(box.conf[0])
                
                # Configurable confidence threshold
                confidence_threshold = 0.5
                if confidence > confidence_threshold:
                    # Get box coordinates
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                    
                    # Calculate box dimensions
                    box_width = x2 - x1
                    box_height = y2 - y1
                    
                    # Filter out very small detections
                    if box_width < 30 or box_height < 50:
                        continue
                    
                    # Draw the bounding box
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # Add label with confidence
                    label = f"Person {confidence:.2f}"
                    cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                    
                    person_detected = True
                    person_count += 1
        
        # Apply temporal consistency check
        if person_detected:
            # Initialize detection history for this camera if not exists
            if camera_id not in detection_history:
                detection_history[camera_id] = []

            # Add current detection to history
            current_time = time.time()
            detection_history[camera_id].append(current_time)
            
            # Keep only detections from the last 10 seconds
            detection_history[camera_id] = [
                t for t in detection_history[camera_id]
                if current_time - t <= 10.0
            ]

            # Require at least 2 detections in the last 6 seconds for confirmation
            recent_detections = [
                t for t in detection_history[camera_id]
                if current_time - t <= 6.0
            ]

            # Only proceed with recording if we have enough consistent detections
            if len(recent_detections) < 2:
                person_detected = False  # Don't record this detection yet
                person_count = 0
            else:
                # Record detection event with timestamp and image
                if camera_id in cameras:
                    # Get current timestamp
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    # Encode image to JPEG for storage
                    _, buffer = cv2.imencode('.jpg', frame)
                    image_data = buffer.tobytes()
                    
                    # Create detection event
                    event = {
                        'timestamp': timestamp,
                        'person_count': person_count,
                        'image_data': image_data
                    }
                    
                    # Add to camera's detection events (limit to 50 most recent)
                    cameras[camera_id]['detection_events'].append(event)
                    if len(cameras[camera_id]['detection_events']) > 50:
                        cameras[camera_id]['detection_events'] = cameras[camera_id]['detection_events'][-50:]
                    
                    # Update hourly stats
                    current_hour = datetime.now().strftime("%Y-%m-%d %H")
                    current_count = cameras[camera_id]['hourly_stats'].get(current_hour, 0)
                    cameras[camera_id]['hourly_stats'][current_hour] = max(current_count, person_count)
                    
                    # Update minute presence
                    current_minute = datetime.now().strftime("%H:%M")
                    cameras[camera_id]['minute_presence'].add(current_minute)
                    
                    # Update daily minute presence
                    current_date = datetime.now().strftime("%Y-%m-%d")
                    if current_date not in cameras[camera_id]['daily_minute_presence']:
                        cameras[camera_id]['daily_minute_presence'][current_date] = set()
                    cameras[camera_id]['daily_minute_presence'][current_date].add(current_minute)
                    
                    # Save cameras to persist detection events
                    save_cameras()
                
        return frame, person_count
    except Exception as e:
        print(f"Error in detect_humans: {e}")
        return frame, 0

def capture_frames(camera_id, rtsp_url):
    camera = cameras[camera_id]
    reconnect_delay = 3  # Reduced delay for faster reconnection

    while camera['running']:
        cap = None
        try:
            print(f"Connecting to camera {camera['name']} at {rtsp_url}")

            # Use OpenCV's VideoCapture with optimized settings for HEVC/H.265 streams
            cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)

            # Aggressive settings to minimize latency and handle HEVC properly
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimal buffer - critical for low latency
            cap.set(cv2.CAP_PROP_FPS, 8)  # Even lower FPS for HEVC stability
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

            # HEVC/H.265 optimized settings
            # Don't force codec - let FFmpeg auto-detect and handle HEVC properly
            # cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '5'))  # HEVC

            # Additional settings for HEVC stream stability
            cap.set(cv2.CAP_PROP_CONVERT_RGB, 1)  # Ensure RGB conversion
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)  # 5 second timeout
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)  # 3 second read timeout

            if not cap.isOpened():
                print(f"Failed to open camera {camera['name']}")
                time.sleep(reconnect_delay)
                continue

            frame_count = 0
            consecutive_errors = 0
            last_good_frame = None
            last_process_time = time.time()
            last_stream_time = time.time()
            frame_skip_counter = 0

            print(f"Successfully connected to camera {camera['name']}")

            while camera['running'] and consecutive_errors < 8:
                current_time = time.time()

                ret, frame = cap.read()

                if not ret or frame is None:
                    consecutive_errors += 1
                    print(f"Frame read error for camera {camera['name']}, error count: {consecutive_errors}")

                    # For HEVC streams, try to skip several frames to get past problematic sections
                    try:
                        for _ in range(3):  # Skip 3 frames to get past bad HEVC frames
                            cap.grab()
                    except:
                        pass

                    # Use the last good frame if available
                    if last_good_frame is not None and not camera['queue'].full():
                        try:
                            camera['queue'].put(last_good_frame.copy(), timeout=0.05)
                        except:
                            pass

                    time.sleep(0.05)
                    continue

                # Check frame validity
                if frame.size == 0:
                    consecutive_errors += 1
                    continue

                # Reset error counter since we got a good frame
                consecutive_errors = 0
                frame_count += 1
                frame_skip_counter += 1

                # Skip frames more aggressively for HEVC streams (keep every 4th frame)
                # This helps avoid problematic frames with invalid cu_qp_delta values
                if frame_skip_counter % 4 != 0:
                    continue

                # Control streaming rate - aim for ~5-8 FPS to browser
                if current_time - last_stream_time < 0.125:  # ~8 FPS max
                    continue

                last_stream_time = current_time

                try:
                    # Resize frame to consistent size with faster interpolation
                    frame = cv2.resize(frame, (640, 480), interpolation=cv2.INTER_NEAREST)

                    # Process frames for detection at a controlled rate (every 2 seconds)
                    if current_time - last_process_time >= 2.0:
                        last_process_time = current_time

                        # Apply human detection if enabled
                        if camera['detection_status']:
                            processed_frame, _ = detect_humans(frame.copy(), camera_id)
                        else:
                            processed_frame = frame.copy()

                        # Store this as our last good frame
                        last_good_frame = processed_frame.copy()
                    else:
                        # Use the current frame for display without detection processing
                        processed_frame = frame.copy()
                        if last_good_frame is None:
                            last_good_frame = processed_frame.copy()

                    # Clear queue if it's getting full to prevent lag buildup
                    if camera['queue'].qsize() > 2:
                        try:
                            # Remove old frames
                            while camera['queue'].qsize() > 1:
                                camera['queue'].get_nowait()
                        except:
                            pass

                    # Add to queue with minimal timeout
                    if not camera['queue'].full():
                        try:
                            camera['queue'].put(processed_frame, timeout=0.01)
                        except:
                            # Queue operation failed, skip this frame
                            pass

                except Exception as e:
                    print(f"Frame processing error for camera {camera['name']}: {e}")
                    if last_good_frame is not None and not camera['queue'].full():
                        try:
                            camera['queue'].put(last_good_frame.copy(), timeout=0.01)
                        except:
                            pass

            print(f"Camera {camera['name']} disconnected, reconnecting in {reconnect_delay} seconds...")

        except Exception as e:
            print(f"Capture error for camera {camera['name']}: {e}")

        finally:
            if cap is not None:
                cap.release()
            time.sleep(reconnect_delay)

def generate_frames(camera_id):
    camera = cameras[camera_id]

    # Default frame to show when no stream is available
    default_frame = create_default_frame(camera['name'])
    last_frame = default_frame.copy()
    frame_count = 0
    last_yield_time = time.time()

    while camera['running']:
        try:
            # Use shorter timeout to prevent lag buildup
            frame = camera['queue'].get(timeout=0.5)

            # Clear any additional frames in queue to prevent lag
            while not camera['queue'].empty():
                try:
                    frame = camera['queue'].get_nowait()  # Get the most recent frame
                except queue.Empty:
                    break

            # Validate frame
            if frame is None or frame.size == 0:
                frame = last_frame.copy()
            else:
                # Update last good frame
                last_frame = frame.copy()
                frame_count += 1

            # Control output frame rate to prevent browser lag
            current_time = time.time()
            if current_time - last_yield_time < 0.1:  # Max 10 FPS to browser
                time.sleep(0.05)
                continue

            last_yield_time = current_time

            # Ensure frame is the right size
            if frame.shape[:2] != (480, 640):
                frame = cv2.resize(frame, (640, 480), interpolation=cv2.INTER_NEAREST)

            # Encode with optimized settings for low latency
            encode_params = [
                cv2.IMWRITE_JPEG_QUALITY, 75,  # Lower quality for faster encoding
                cv2.IMWRITE_JPEG_OPTIMIZE, 0,  # Disable optimization for speed
            ]

            ret, buffer = cv2.imencode('.jpg', frame, encode_params)

            if not ret or buffer is None:
                # Use last good frame
                ret, buffer = cv2.imencode('.jpg', last_frame, encode_params)
                if not ret:
                    continue

            frame_bytes = buffer.tobytes()

            # Send frame with minimal headers for speed
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

        except queue.Empty:
            # Send last good frame on timeout with reduced frequency
            current_time = time.time()
            if current_time - last_yield_time >= 0.2:  # Only send fallback every 200ms
                try:
                    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 75]
                    ret, buffer = cv2.imencode('.jpg', last_frame, encode_params)
                    if ret:
                        frame_bytes = buffer.tobytes()
                        yield (b'--frame\r\n'
                               b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                        last_yield_time = current_time
                except Exception as e:
                    print(f"Error sending fallback frame: {e}")

            time.sleep(0.05)

        except Exception as e:
            print(f"Stream error for camera {camera['name']}: {e}")
            time.sleep(0.05)

def create_default_frame(camera_name):
    # Create a blank frame with text when camera is not available
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    frame[:] = (70, 70, 70)  # Dark gray background

    # Add text
    cv2.putText(frame, f"Connecting to {camera_name}...",
                (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 1,
                (255, 255, 255), 2)

    return frame

# Login route
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if username == LOGIN_USERNAME and password == LOGIN_PASSWORD:
            session['logged_in'] = True
            return redirect(url_for('index'))
        else:
            return render_template('login.html', error='Invalid username or password')

    # If already logged in, redirect to dashboard
    if 'logged_in' in session:
        return redirect(url_for('index'))

    return render_template('login.html')

# Logout route
@app.route('/logout')
def logout():
    session.pop('logged_in', None)
    return redirect(url_for('login'))

@app.route('/')
@login_required
def index():
    return render_template('index.html', cameras=cameras, net=yolo_model)

@app.route('/add_camera', methods=['POST'])
@login_required
def add_camera():
    camera_name = request.form.get('camera_name')
    camera_url = request.form.get('camera_url')

    if not camera_name or not camera_url:
        return redirect(url_for('index'))

    camera_id = str(uuid.uuid4())
    cameras[camera_id] = {
        'name': camera_name,
        'url': camera_url,
        'queue': queue.Queue(maxsize=2),  # Reduced queue size to minimize lag
        'running': True,
        'detection_status': True,  # Always enable detection
        'notification_status': False,  # Notifications disabled by default
        'detection_events': [],
        'hourly_stats': {},  # Store hourly maximum occupancy
        'minute_presence': set(),  # Store minutes when people were detected (legacy)
        'daily_minute_presence': {},  # Store date-based minute presence
        'last_detection_second': None  # Track the last second when a detection was recorded
    }

    # Start capture thread for this camera
    thread = threading.Thread(target=capture_frames, args=(camera_id, camera_url))
    thread.daemon = True
    thread.start()

    # Save cameras to persistent storage
    save_cameras()

    return redirect(url_for('index'))

@app.route('/video_feed/<camera_id>')
@login_required
def video_feed(camera_id):
    if camera_id in cameras:
        response = Response(generate_frames(camera_id),
                           mimetype='multipart/x-mixed-replace; boundary=frame')

        # Add headers to improve streaming performance and reduce glitches
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        response.headers['Connection'] = 'keep-alive'
        response.headers['X-Accel-Buffering'] = 'no'  # Disable nginx buffering

        return response
    return "Camera not found", 404

@app.route('/view_camera/<camera_id>')
@login_required
def view_camera(camera_id):
    if camera_id in cameras:
        return render_template('view_camera.html', camera=cameras[camera_id], camera_id=camera_id)
    return redirect(url_for('index'))

@app.route('/remove_camera/<camera_id>', methods=['POST'])
@login_required
def remove_camera(camera_id):
    if camera_id in cameras:
        # Stop the camera thread
        cameras[camera_id]['running'] = False

        # Wait a moment for the thread to clean up
        time.sleep(0.5)

        # Remove the camera from the dictionary
        del cameras[camera_id]

        # Save cameras to persistent storage
        save_cameras()

    return redirect(url_for('index'))

# Remove this route since detection is always enabled
# @app.route('/toggle_detection/<camera_id>', methods=['POST'])
# def toggle_detection(camera_id):
#     if camera_id in cameras:
#         cameras[camera_id]['detection_status'] = not cameras[camera_id]['detection_status']
#         return redirect(url_for('index'))
#     return "Camera not found", 404

@app.route('/toggle_notifications/<camera_id>', methods=['POST'])
@login_required
def toggle_notifications(camera_id):
    if camera_id in cameras:
        cameras[camera_id]['notification_status'] = not cameras[camera_id]['notification_status']
        # Save cameras to persistent storage
        save_cameras()
        return redirect(url_for('index'))
    return "Camera not found", 404

# Function to save historical data
def save_historical_data(camera_id, date):
    if camera_id in cameras:
        camera = cameras[camera_id]

        # Prepare hourly data for bar chart
        hourly_data = []
        for hour in range(24):
            hour_key = f"{date} {hour:02d}"
            count = camera['hourly_stats'].get(hour_key, 0)
            hourly_data.append({
                'hour': f"{hour:02d}:00",
                'count': count,
                'label': f"{hour:02d}:00"
            })

        # Prepare minute data for circular/polar chart
        minute_data = []
        circular_data = []

        # Get minute presence for the specific date
        date_minute_presence = set()
        if 'daily_minute_presence' in camera and date in camera['daily_minute_presence']:
            date_minute_presence = camera['daily_minute_presence'][date]

        for hour in range(24):
            for minute in range(60):
                time_key = f"{hour:02d}:{minute:02d}"
                present = 1 if time_key in date_minute_presence else 0
                minute_data.append({
                    'time': time_key,
                    'present': present,
                    'hour': hour,
                    'minute': minute
                })

        # Create circular chart data (24 hours with presence percentage)
        for hour in range(24):
            hour_presence = 0
            for minute in range(60):
                time_key = f"{hour:02d}:{minute:02d}"
                if time_key in date_minute_presence:
                    hour_presence += 1

            # Calculate percentage of presence in this hour
            presence_percentage = (hour_presence / 60) * 100
            circular_data.append({
                'hour': hour,
                'label': f"{hour:02d}:00",
                'presence_percentage': round(presence_percentage, 2),
                'presence_minutes': hour_presence,
                'angle': hour * 15  # 360/24 = 15 degrees per hour
            })

        # Filter detection events for this date
        date_events = []
        for event in camera.get('detection_events', []):
            event_date = event.get('timestamp', '').split(' ')[0]
            if event_date == date:
                date_events.append(event)

        # Create data object with enhanced structure for charts
        data = {
            'date': date,
            'camera_id': camera_id,
            'camera_name': camera['name'],
            'hourly_data': hourly_data,
            'minute_data': minute_data,
            'circular_data': circular_data,
            'detection_events': date_events,  # Add detection events
            'summary': {
                'total_detections': len(date_events),
                'peak_hour': max(hourly_data, key=lambda x: x['count'])['hour'] if hourly_data else '00:00',
                'total_presence_minutes': len(date_minute_presence),
                'presence_percentage': round((len(date_minute_presence) / 1440) * 100, 2)  # 1440 minutes in a day
            }
        }

        # Save to file with pretty formatting
        filename = f"history_data/{camera_id}_{date.replace('-', '')}.json"
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=4)
            print(f"Successfully saved historical data for {camera['name']} on {date}")
            return data
        except Exception as e:
            print(f"Error saving historical data: {e}")
            return None
    return None

# Function to load historical data
def load_historical_data(camera_id, date):
    filename = f"history_data/{camera_id}_{date.replace('-', '')}.json"

    if os.path.exists(filename):
        with open(filename, 'r') as f:
            return json.load(f)

    # If file doesn't exist but it's today, generate it
    today = datetime.now().strftime("%Y-%m-%d")
    if date == today and camera_id in cameras:
        return save_historical_data(camera_id, date)

    # Return empty data structure with enhanced format
    empty_hourly_data = [{'hour': f"{hour:02d}:00", 'count': 0, 'label': f"{hour:02d}:00"} for hour in range(24)]
    empty_minute_data = [{'time': f"{hour:02d}:{minute:02d}", 'present': 0, 'hour': hour, 'minute': minute}
                        for hour in range(24) for minute in range(60)]
    empty_circular_data = [{
        'hour': hour,
        'label': f"{hour:02d}:00",
        'presence_percentage': 0,
        'presence_minutes': 0,
        'angle': hour * 15
    } for hour in range(24)]

    return {
        'date': date,
        'camera_id': camera_id,
        'camera_name': 'Unknown',
        'hourly_data': empty_hourly_data,
        'minute_data': empty_minute_data,
        'circular_data': empty_circular_data,
        'summary': {
            'total_detections': 0,
            'peak_hour': '00:00',
            'total_presence_minutes': 0,
            'presence_percentage': 0
        }
    }

# Add a route to get historical data
@app.route('/get_history_data/<camera_id>')
@login_required
def get_history_data(camera_id):
    date = request.args.get('date', datetime.now().strftime("%Y-%m-%d"))

    try:
        # Check if it's today's data
        today = datetime.now().strftime("%Y-%m-%d")
        if date == today and camera_id in cameras:
            # For today, get current data
            hourly_data = []
            for hour in range(24):
                hour_key = f"{date} {hour:02d}"
                count = cameras[camera_id]['hourly_stats'].get(hour_key, 0)
                hourly_data.append({
                    'hour': f"{hour:02d}:00",
                    'count': count
                })

            # Get today's minute presence data
            today_minute_presence = set()
            if 'daily_minute_presence' in cameras[camera_id] and date in cameras[camera_id]['daily_minute_presence']:
                today_minute_presence = cameras[camera_id]['daily_minute_presence'][date]

            minute_data = []
            for hour in range(24):
                for minute in range(60):
                    time_key = f"{hour:02d}:{minute:02d}"
                    present = 1 if time_key in today_minute_presence else 0
                    minute_data.append({
                        'time': time_key,
                        'present': present
                    })

            # Save today's data for future reference
            save_historical_data(camera_id, date)

            return jsonify({
                'date': date,
                'hourly_data': hourly_data,
                'minute_data': minute_data
            })
        else:
            # For past dates, load from file
            data = load_historical_data(camera_id, date)
            return jsonify({
                'date': date,
                'hourly_data': data['hourly_data'],
                'minute_data': data['minute_data'],
                'circular_data': data.get('circular_data', []),
                'summary': data.get('summary', {})
            })
    except Exception as e:
        print(f"Error getting history data: {e}")
        # Return empty data structure instead of error
        empty_hourly_data = [{'hour': f"{hour:02d}:00", 'count': 0} for hour in range(24)]
        empty_minute_data = [{'time': f"{hour:02d}:{minute:02d}", 'present': 0}
                            for hour in range(24) for minute in range(60)]

        return jsonify({
            'date': date,
            'hourly_data': empty_hourly_data,
            'minute_data': empty_minute_data
        }), 200  # Return 200 OK even for empty data

@app.route('/view_history/<camera_id>')
@login_required
def view_history(camera_id):
    if camera_id in cameras:
        # Get today's date
        today = datetime.now().strftime("%Y-%m-%d")

        # Prepare hourly data for the chart
        hourly_data = []
        for hour in range(24):
            hour_key = f"{today} {hour:02d}"
            count = cameras[camera_id]['hourly_stats'].get(hour_key, 0)
            hourly_data.append({
                'hour': f"{hour:02d}:00",
                'count': count
            })

        # Prepare minute presence data for today
        today_minute_presence = set()
        if 'daily_minute_presence' in cameras[camera_id] and today in cameras[camera_id]['daily_minute_presence']:
            today_minute_presence = cameras[camera_id]['daily_minute_presence'][today]

        minute_data = []
        for hour in range(24):
            for minute in range(60):
                time_key = f"{hour:02d}:{minute:02d}"
                present = 1 if time_key in today_minute_presence else 0
                minute_data.append({
                    'time': time_key,
                    'present': present
                })

        # Save current data to ensure it's available for future viewing
        save_historical_data(camera_id, today)
        
        # Load the past 50 detection events
        events = load_detection_events(camera_id, 50)

        return render_template('history.html',
                              camera=cameras[camera_id],
                              camera_id=camera_id,
                              events=events,  # Use the loaded events
                              hourly_data=json.dumps(hourly_data),
                              minute_data=json.dumps(minute_data),
                              today=today)
    return redirect(url_for('index'))

@app.route('/manage_recipients')
@login_required
def manage_recipients():
    global digest_cooldown_minutes, email_interval_hours, email_interval_minutes

    # Load settings if they exist
    settings = load_settings()
    cooldown = settings.get('digest_cooldown_minutes', digest_cooldown_minutes)
    interval_hours = settings.get('email_interval_hours', email_interval_hours)
    interval_minutes = settings.get('email_interval_minutes', email_interval_minutes)

    return render_template('manage_recipients.html',
                          cooldown_minutes=cooldown,
                          interval_hours=interval_hours,
                          interval_minutes=interval_minutes)

# Route to send a test notification
@app.route('/send_test_notification', methods=['POST'])
@login_required
def send_test_notification():
    try:
        # Get recipient ID from request
        data = request.json
        recipient_id = data.get('recipient_id')

        if not recipient_id:
            return jsonify({"success": False, "message": "Recipient ID is required"}), 400

        # Load recipients
        recipients_data = load_recipients()

        # Find the recipient
        recipient = None
        for r in recipients_data["recipients"]:
            if r["id"] == recipient_id:
                recipient = r
                break

        if not recipient:
            return jsonify({"success": False, "message": "Recipient not found"}), 404

        # Check if recipient has email
        if not recipient.get('email'):
            return jsonify({"success": False, "message": "Recipient does not have an email address"}), 400

        # Format the message
        test_message = f"Hello {recipient['name']},\n\nThis is a test notification from your CCTV Camera Management System.\n\nRegards,\nCCTV System"

        # Log the test notification
        print(f"TEST NOTIFICATION: Email to {recipient['name']} ({recipient['email']})")

        # Send the email
        subject = "CCTV System - Test Notification"
        success, error = send_email_notification(recipient['email'], recipient['name'], subject, test_message)

        # Log the notification
        log_notification(f"TEST EMAIL: {test_message}", [recipient])

        if success:
            return jsonify({
                "success": True,
                "message": f"Test notification sent to {recipient['name']} via email"
            })
        else:
            return jsonify({
                "success": False,
                "message": f"Failed to send email: {error}"
            }), 500

    except Exception as e:
        print(f"Error sending test notification: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# Route to get all recipients
@app.route('/get_recipients')
@login_required
def get_recipients():
    try:
        recipients_data = load_recipients()
        return jsonify(recipients_data)
    except Exception as e:
        print(f"Error getting recipients: {e}")
        return jsonify({"recipients": []})

# Route to add a new recipient
@app.route('/add_recipient', methods=['POST'])
@login_required
def add_recipient():
    try:
        # Get data from request
        data = request.json
        name = data.get('name')
        email = data.get('email')

        if not name or not email:
            return jsonify({"success": False, "message": "Name and email are required"}), 400

        # Validate email format (basic validation)
        if '@' not in email or '.' not in email:
            return jsonify({"success": False, "message": "Invalid email format"}), 400

        # Load existing recipients
        recipients_data = load_recipients()

        # Check if email already exists
        for recipient in recipients_data["recipients"]:
            if recipient.get("email") == email:
                return jsonify({"success": False, "message": "This email is already registered"}), 400

        # Add new recipient
        new_recipient = {
            "id": str(uuid.uuid4()),
            "name": name,
            "email": email,
            "added_on": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        recipients_data["recipients"].append(new_recipient)

        # Save updated recipients
        with open('notification_data/recipients.json', 'w') as f:
            json.dump(recipients_data, f, indent=4)

        return jsonify({"success": True, "recipient": new_recipient})

    except Exception as e:
        print(f"Error adding recipient: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# Route to delete a recipient
@app.route('/delete_recipient/<recipient_id>', methods=['POST'])
@login_required
def delete_recipient(recipient_id):
    try:
        # Load recipients
        recipients_data = load_recipients()

        # Find and remove the recipient
        recipients_data["recipients"] = [r for r in recipients_data["recipients"] if r["id"] != recipient_id]

        # Save updated recipients
        with open('notification_data/recipients.json', 'w') as f:
            json.dump(recipients_data, f, indent=4)

        return jsonify({"success": True})

    except Exception as e:
        print(f"Error deleting recipient: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

def init_recipients_file():
    """Initialize the recipients file if it doesn't exist"""
    recipients_dir = 'notification_data'
    recipients_file = f'{recipients_dir}/recipients.json'

    # Create directory if it doesn't exist
    if not os.path.exists(recipients_dir):
        os.makedirs(recipients_dir)

    # Create recipients file with empty structure if it doesn't exist
    if not os.path.exists(recipients_file):
        with open(recipients_file, 'w') as f:
            json.dump({"recipients": []}, f)
        print(f"Created empty recipients file at {recipients_file}")

# Load recipients from file
def load_recipients():
    """Load recipients from the JSON file"""
    recipients_file = 'notification_data/recipients.json'

    if os.path.exists(recipients_file):
        with open(recipients_file, 'r') as f:
            return json.load(f)
    else:
        # Initialize the file and return empty structure
        init_recipients_file()
        return {"recipients": []}

# Define the log_notification function
def log_notification(message, recipients):
    """Log a notification to the notification log file"""
    log_file = 'notification_data/notification_log.json'

    # Create log entry
    log_entry = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message": message,
        "recipients": recipients
    }

    # Load existing log
    log_data = {"notifications": []}
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r') as f:
                log_data = json.load(f)
        except json.JSONDecodeError:
            # If file is corrupted, start with empty log
            log_data = {"notifications": []}

    # Add new entry
    log_data["notifications"].append(log_entry)

    # Save updated log
    with open(log_file, 'w') as f:
        json.dump(log_data, f, indent=4)

    return log_entry

def send_whatsapp_message(phone_number, message):
    """
    Send a WhatsApp message using PyWhatKit

    Args:
        phone_number: Phone number in format +91XXXXXXXXXX (without spaces)
        message: Message text to send

    Returns:
        success: Boolean indicating if the message was sent successfully
        error: Error message if any
    """
    try:
        # Remove any spaces from the phone number
        phone_number = phone_number.replace(" ", "")

        # PyWhatKit requires phone number without the + symbol
        if phone_number.startswith('+'):
            phone_number = phone_number[1:]

        # Get current time to send the message immediately
        now = datetime.now()

        # Add 1 minute to current time (PyWhatKit needs a future time)
        send_time_hour = now.hour
        send_time_minute = now.minute + 1

        # Handle minute overflow
        if send_time_minute >= 60:
            send_time_minute = 0
            send_time_hour += 1

        # Handle hour overflow
        if send_time_hour >= 24:
            send_time_hour = 0

        # Send the WhatsApp message
        pywhatkit.sendwhatmsg(
            phone_no=phone_number,
            message=message,
            time_hour=send_time_hour,
            time_min=send_time_minute,
            wait_time=15,  # Wait 15 seconds before sending
            tab_close=True,  # Close the tab after sending
            close_time=3  # Wait 3 seconds before closing the tab
        )

        return True, None
    except Exception as e:
        print(f"Error sending WhatsApp message: {e}")
        return False, str(e)

# Initialize yagmail SMTP client - correct format
try:
    yag = yagmail.SMTP(
        user='<EMAIL>',  # Replace with your actual email
        password='dkoa rqsb mogu nfoj'   # Replace with your 16-character app password
    )
except Exception as e:
    print(f"Error initializing yagmail: {e}")
    print("Email notifications will not be available.")
    yag = None

# Function to send email using yagmail
def send_email_notification(recipient_email, recipient_name, subject, message):
    try:
        # Check if yagmail is initialized
        if yag is None:
            return False, "Email client not initialized. Check your credentials."

        # Send the email
        yag.send(
            to=recipient_email,
            subject=subject,
            contents=message
        )
        return True, None
    except Exception as e:
        print(f"Error sending email: {e}")
        return False, str(e)

def send_detection_notification(camera_id, person_count, timestamp):
    """Send notification to all recipients when people are detected"""
    if camera_id not in cameras:
        return

    camera = cameras[camera_id]
    camera_name = camera['name']

    # Format the message
    subject = f"CCTV Alert: {person_count} person(s) detected"
    message = f"""
Alert from your CCTV System!

{person_count} person(s) detected on camera '{camera_name}' at {timestamp}.

Please check your CCTV system for more details.

Regards,
CCTV Notification System
"""

    # Load recipients
    recipients_data = load_recipients()

    # Send to all recipients
    for recipient in recipients_data.get("recipients", []):
        if recipient.get('email'):
            try:
                # Send the email
                success, error = send_email_notification(
                    recipient['email'],
                    recipient['name'],
                    subject,
                    message
                )

                if success:
                    print(f"Notification sent to {recipient['name']} ({recipient['email']})")
                else:
                    print(f"Failed to send notification to {recipient['name']}: {error}")

            except Exception as e:
                print(f"Error sending notification to {recipient['name']}: {e}")

    # Log the notification
    log_notification(
        f"Alert! {person_count} person(s) detected on camera '{camera_name}' at {timestamp}",
        recipients_data.get("recipients", [])
    )

# Global variables for digest system
digest_events = {}  # Store events by camera
digest_lock = threading.Lock()  # Thread safety
digest_timer = None  # Timer for sending digests
digest_cooldown_minutes = 1  # Default cooldown of 1 minute
email_interval_hours = 0  # Default to 0 hours
email_interval_minutes = 2  # Default to 2 minutes

# Route to set digest cooldown
@app.route('/set_digest_cooldown', methods=['POST'])
@login_required
def set_digest_cooldown():
    global digest_cooldown_minutes

    try:
        data = request.json
        cooldown_minutes = data.get('cooldown_minutes', 1)

        # Validate input
        if not isinstance(cooldown_minutes, int) or cooldown_minutes < 1 or cooldown_minutes > 60:
            return jsonify({
                "success": False,
                "message": "Cooldown must be between 1 and 60 minutes"
            }), 400

        # Update the global cooldown setting
        digest_cooldown_minutes = cooldown_minutes

        # Save the setting to a file for persistence
        save_settings({
            "digest_cooldown_minutes": digest_cooldown_minutes
        })

        return jsonify({
            "success": True,
            "message": f"Digest cooldown set to {cooldown_minutes} minute(s)"
        })

    except Exception as e:
        print(f"Error setting digest cooldown: {e}")
        return jsonify({
            "success": False,
            "message": str(e)
        }), 500

# Route to set email sending interval
@app.route('/set_email_interval', methods=['POST'])
@login_required
def set_email_interval():
    global email_interval_hours, email_interval_minutes, digest_timer

    try:
        data = request.json
        interval_hours = data.get('interval_hours', 0)
        interval_minutes = data.get('interval_minutes', 2)

        # Validate input
        if not isinstance(interval_hours, int) or interval_hours < 0 or interval_hours > 23:
            return jsonify({
                "success": False,
                "message": "Hours must be between 0 and 23"
            }), 400

        if not isinstance(interval_minutes, int) or interval_minutes < 0 or interval_minutes > 59:
            return jsonify({
                "success": False,
                "message": "Minutes must be between 0 and 59"
            }), 400

        # Ensure at least 1 minute total interval
        if interval_hours == 0 and interval_minutes < 1:
            return jsonify({
                "success": False,
                "message": "Total interval must be at least 1 minute"
            }), 400

        # Update the global interval settings
        email_interval_hours = interval_hours
        email_interval_minutes = interval_minutes

        # Save the settings to a file for persistence
        save_settings({
            "email_interval_hours": email_interval_hours,
            "email_interval_minutes": email_interval_minutes
        })

        # Restart the digest timer with the new interval
        if digest_timer is not None:
            digest_timer.cancel()

        # Calculate total seconds for the timer
        total_seconds = (email_interval_hours * 3600) + (email_interval_minutes * 60)

        digest_timer = threading.Timer(total_seconds, send_digest_emails)
        digest_timer.daemon = True
        digest_timer.start()

        print(f"Digest timer reset - will send emails every {email_interval_hours} hour(s) and {email_interval_minutes} minute(s)")

        return jsonify({
            "success": True,
            "message": f"Email interval set to {interval_hours} hour(s) and {interval_minutes} minute(s)"
        })

    except Exception as e:
        print(f"Error setting email interval: {e}")
        return jsonify({
            "success": False,
            "message": str(e)
        }), 500

# Function to save settings
def save_settings(settings):
    """Save settings to a JSON file"""
    settings_file = 'notification_data/settings.json'

    # Create settings directory if it doesn't exist
    if not os.path.exists('notification_data'):
        os.makedirs('notification_data')

    # Load existing settings if file exists
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                existing_settings = json.load(f)
        except json.JSONDecodeError:
            existing_settings = {}
    else:
        existing_settings = {}

    # Update with new settings
    existing_settings.update(settings)

    # Save updated settings
    with open(settings_file, 'w') as f:
        json.dump(existing_settings, f, indent=4)

# Function to load settings
def load_settings():
    """Load settings from the JSON file"""
    settings_file = 'notification_data/settings.json'

    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            return {}
    else:
        return {}

def init_digest_system():
    """Initialize the digest system"""
    global digest_timer, email_interval_hours, email_interval_minutes

    # Clear any existing events
    with digest_lock:
        digest_events.clear()

    # Calculate total seconds for the timer
    total_seconds = (email_interval_hours * 3600) + (email_interval_minutes * 60)

    # Start the digest timer
    digest_timer = threading.Timer(total_seconds, send_digest_emails)
    digest_timer.daemon = True
    digest_timer.start()
    print(f"Digest system initialized - will send emails every {email_interval_hours} hour(s) and {email_interval_minutes} minute(s)")

def add_to_digest(camera_id, camera_name, image_data, person_count, timestamp):
    """Add a detection event to the digest with time-based throttling"""
    global digest_cooldown_minutes

    with digest_lock:
        # Initialize camera entry if it doesn't exist
        if camera_id not in digest_events:
            digest_events[camera_id] = {
                'name': camera_name,
                'events': [],
                'last_digest_time': None  # Track the last time when an image was added to digest
            }

        # Get current time
        current_time = datetime.now() if isinstance(timestamp, str) else timestamp

        # Check if we need to respect the cooldown period
        if digest_events[camera_id]['last_digest_time'] is not None:
            # Calculate time difference in minutes
            time_diff = (current_time - digest_events[camera_id]['last_digest_time']).total_seconds() / 60

            # If we haven't waited long enough, skip this image
            if time_diff < digest_cooldown_minutes:
                print(f"Skipping digest image for camera {camera_name} - cooldown period of {digest_cooldown_minutes} minute(s) not elapsed")
                return

        # Update the last digest time
        digest_events[camera_id]['last_digest_time'] = current_time

        # Add the event
        digest_events[camera_id]['events'].append({
            'timestamp': timestamp if isinstance(timestamp, str) else timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            'image_data': image_data,  # This should be the raw bytes of the JPEG image
            'person_count': person_count
        })

        print(f"Added image to digest for camera {camera_name} at {current_time.strftime('%Y-%m-%d %H:%M:%S')} with {person_count} person(s)")

def send_digest_emails():
    """Send digest emails and reset the timer"""
    global digest_timer, email_interval_hours, email_interval_minutes

    try:
        with digest_lock:
            # Check if we have any events
            if not digest_events:
                print("No detection events to send in digest")
            else:
                # Load recipients
                recipients_data = load_recipients()
                recipients = recipients_data.get("recipients", [])

                if not recipients:
                    print("No recipients configured for digest emails")
                else:
                    # Send digest to each recipient
                    for recipient in recipients:
                        if recipient.get('email'):
                            send_digest_to_recipient(recipient, digest_events)

            # Clear events after sending
            digest_events.clear()

    except Exception as e:
        print(f"Error sending digest emails: {e}")

    finally:
        # Calculate total seconds for the timer
        total_seconds = (email_interval_hours * 3600) + (email_interval_minutes * 60)

        # Restart the timer
        digest_timer = threading.Timer(total_seconds, send_digest_emails)
        digest_timer.daemon = True
        digest_timer.start()

def send_digest_to_recipient(recipient, digest_data):
    """Send a digest email to a specific recipient"""
    try:
        # Skip if no events
        total_events = sum(len(camera_data['events']) for camera_data in digest_data.values())
        if total_events == 0:
            return

        print(f"Starting to send digest to {recipient['name']} ({recipient['email']})")

        # Create a multipart email message
        msg = MIMEMultipart('related')

        # Update subject line to reflect the configured interval
        interval_text = ""
        if email_interval_hours > 0:
            interval_text += f"{email_interval_hours} hour"
            if email_interval_hours > 1:
                interval_text += "s"
        if email_interval_minutes > 0:
            if interval_text:
                interval_text += " and "
            interval_text += f"{email_interval_minutes} minute"
            if email_interval_minutes > 1:
                interval_text += "s"

        msg['Subject'] = f"CCTV Digest: {total_events} Detection Events (Last {interval_text})"
        msg['From'] = '<EMAIL>'  # Use the same email as in yag
        msg['To'] = recipient['email']

        # Create the HTML part
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <style>
    body {{font-family: Arial, sans-serif; margin: 0; padding: 20px;}}
    .camera-section {{margin-bottom: 30px;}}
    .camera-title {{background-color: #f0f0f0; padding: 10px;}}
    .event-card {{border: 1px solid #ddd; padding: 10px; margin-bottom: 10px;}}
    .event-time {{font-weight: bold; margin-bottom: 5px;}}
    img {{max-width: 100%; height: auto;}}
    .digest-note {{color: #666; font-style: italic; margin-bottom: 20px;}}
    </style>
</head>
<body>
    <h1>CCTV Detection Digest</h1>
    <p>Hello {recipient['name']},</p>
    <p>Here is a summary of detection events from the last {interval_text}:</p>
    <p class="digest-note">Note: To reduce email size, only one image is included per {digest_cooldown_minutes} minute(s) for each camera.</p>
"""

        # Add each camera's events
        image_counter = 1
        image_attachments = []

        for camera_id, camera_data in digest_data.items():
            if not camera_data['events']:
                continue

            html_content += f"""
    <div class="camera-section">
        <h2 class="camera-title">Camera: {camera_data['name']}</h2>
        <p>{len(camera_data['events'])} detection events</p>
"""

            # Add each event
            for event in camera_data['events']:
                # Create a unique image ID
                img_id = f"img{image_counter}"
                image_counter += 1

                # Add event to HTML
                html_content += f"""
        <div class="event-card">
            <div class="event-time">{event['timestamp']}</div>
            <div>People detected: {event['person_count']}</div>
            <div><img src="cid:{img_id}" alt="Detection at {event['timestamp']}"></div>
        </div>
"""

                # Create MIMEImage for this event
                image = MIMEImage(event['image_data'])
                image.add_header('Content-ID', f'<{img_id}>')
                image.add_header('Content-Disposition', 'inline')
                image_attachments.append(image)

            html_content += """
    </div>
"""

        # Close HTML
        html_content += """
    <p>Regards,<br>CCTV Notification System</p>
</body>
</html>
"""

        # Attach HTML content
        html_part = MIMEText(html_content, 'html')
        msg.attach(html_part)

        # Attach all images
        for image in image_attachments:
            msg.attach(image)

        # Send the email using SMTP directly
        if yag is not None:
            try:
                # Use yagmail's underlying SMTP connection
                yag.send(
                    to=recipient['email'],
                    subject=f"CCTV Digest: {total_events} Detection Events",
                    contents=html_content,
                    attachments=[{
                        'content': event['image_data'],
                        'inline': True,
                        'content_id': f'img{i+1}'
                    } for i, event in enumerate(
                        [e for cam in digest_data.values() for e in cam['events']]
                    )]
                )
                print(f"Digest email sent to {recipient['name']} ({recipient['email']})")
            except Exception as e:
                print(f"Error sending with yagmail: {e}")

                # Fall back to standard SMTP
                try:
                    smtp = smtplib.SMTP('smtp.gmail.com', 587)
                    smtp.starttls()
                    smtp.login('<EMAIL>', 'dkoa rqsb mogu nfoj')
                    smtp.send_message(msg)
                    smtp.quit()
                    print(f"Digest email sent via SMTP to {recipient['name']} ({recipient['email']})")
                except Exception as smtp_error:
                    print(f"SMTP error: {smtp_error}")
                    raise
        else:
            print(f"Cannot send digest email: yagmail not initialized")

        # Log the notification
        log_notification(
            f"Digest email: {total_events} detection events sent to {recipient['name']}",
            [recipient]
        )

    except Exception as e:
        print(f"Error sending digest to {recipient['name']}: {e}")

# Add this function to load detection events from history data
def load_detection_events(camera_id, limit=50):
    """Load the most recent detection events for a camera"""
    events = []
    
    # First, get any current events from the camera
    if camera_id in cameras:
        current_events = cameras[camera_id].get('detection_events', [])
        events.extend(current_events)
    
    # Then try to load events from historical data files
    history_dir = 'history_data'
    if os.path.exists(history_dir):
        # Get all history files for this camera
        camera_files = [f for f in os.listdir(history_dir) 
                       if f.startswith(camera_id) and f.endswith('.json')]
        
        # Sort by date (newest first)
        camera_files.sort(reverse=True)
        
        # Load events from each file until we have enough
        for file_name in camera_files:
            if len(events) >= limit:
                break
                
            file_path = os.path.join(history_dir, file_name)
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    if 'detection_events' in data:
                        file_events = data['detection_events']
                        events.extend(file_events)
            except Exception as e:
                print(f"Error loading events from {file_path}: {e}")
    
    # Return the most recent events up to the limit
    return events[:limit]

def schedule_email_checking():
    """Schedule periodic email checking"""
    email_check_timer = threading.Timer(300, check_emails_periodically)  # Check every 5 minutes
    email_check_timer.daemon = True
    email_check_timer.start()
    print("Email checking scheduled - will check every 5 minutes")

def check_emails_periodically():
    """Check emails and reschedule the timer"""
    try:
        emails = check_incoming_emails()
        if emails:
            print(f"Received {len(emails)} new emails")
            # Process emails here if needed
    except Exception as e:
        print(f"Error in periodic email check: {e}")
    finally:
        # Reschedule the timer
        schedule_email_checking()

@app.route('/check_emails')
@login_required
def check_emails_route():
    try:
        emails = check_incoming_emails()
        return jsonify({
            "success": True,
            "emails": emails,
            "count": len(emails)
        })
    except Exception as e:
        print(f"Error in check_emails route: {e}")
        return jsonify({
            "success": False,
            "message": str(e)
        }), 500

def check_incoming_emails():
    """Check for incoming emails using IMAP"""
    try:
        # Connect to Gmail's IMAP server
        mail = imaplib.IMAP4_SSL("imap.gmail.com")
        mail.login("<EMAIL>", "dkoa rqsb mogu nfoj")
        mail.select("INBOX")
        
        # Search for unread emails
        status, messages = mail.search(None, "UNSEEN")
        if status != "OK":
            print("No messages found!")
            return []
        
        email_data = []
        for num in messages[0].split():
            # Fetch the email
            status, data = mail.fetch(num, "(RFC822)")
            if status != "OK":
                continue
                
            # Parse the email
            msg = email.message_from_bytes(data[0][1])
            subject = decode_header(msg["Subject"])[0][0]
            if isinstance(subject, bytes):
                subject = subject.decode()
            
            # Get sender
            from_ = msg.get("From")
            
            # Get body
            body = ""
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    if content_type == "text/plain":
                        body = part.get_payload(decode=True).decode()
                        break
            else:
                body = msg.get_payload(decode=True).decode()
            
            email_data.append({
                "from": from_,
                "subject": subject,
                "body": body
            })
            
            # Mark as read
            mail.store(num, "+FLAGS", "\\Seen")
        
        mail.close()
        mail.logout()
        return email_data
        
    except Exception as e:
        print(f"Error checking emails: {e}")
        return []



if __name__ == '__main__':
    # Initialize the recipients file
    init_recipients_file()

    # Create notification log directory if it doesn't exist
    if not os.path.exists('notification_data'):
        os.makedirs('notification_data')

    # Initialize notification log file if it doesn't exist
    if not os.path.exists('notification_data/notification_log.json'):
        with open('notification_data/notification_log.json', 'w') as f:
            json.dump({"notifications": []}, f)

    # Load cameras from persistent storage
    load_cameras()

    # Load settings
    settings = load_settings()
    if 'digest_cooldown_minutes' in settings:
        digest_cooldown_minutes = settings['digest_cooldown_minutes']
    if 'email_interval_hours' in settings:
        email_interval_hours = settings['email_interval_hours']
    if 'email_interval_minutes' in settings:
        email_interval_minutes = settings['email_interval_minutes']

    # Initialize the digest system
    init_digest_system()

    # Initialize midnight cleanup system
    schedule_midnight_cleanup()
    
    # Initialize email checking system
    schedule_email_checking()

    # Use Flask's development server directly
    print("Starting Flask server on http://0.0.0.0:5000")
    app.run(host='0.0.0.0', port=5000, debug=False)  # Set debug=False for more stability

