import os
import urllib.request
import subprocess
import sys
import shutil

print("YOLOv8n Model Downloader")
print("-----------------------")

# Create models directory if it doesn't exist
if not os.path.exists('models'):
    os.makedirs('models')
    print("Created 'models' directory")

# Install ultralytics if not already installed
try:
    import ultralytics
    print("✓ Ultralytics already installed")
except ImportError:
    print("Installing ultralytics package...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "ultralytics"])
        print("✓ Ultralytics installed successfully!")
    except Exception as e:
        print(f"✗ Error installing ultralytics: {e}")
        print("Please install manually with: pip install ultralytics")

# Direct download from GitHub
model_url = "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt"
model_path = "yolov8n.pt"  # Download to current directory first

print(f"Downloading YOLOv8n model from {model_url}...")
try:
    # Direct download with progress reporting
    def report_progress(block_num, block_size, total_size):
        downloaded = block_num * block_size
        percent = min(int(downloaded * 100 / total_size), 100)
        sys.stdout.write(f"\rProgress: {percent}% [{downloaded} / {total_size} bytes]")
        sys.stdout.flush()
    
    urllib.request.urlretrieve(model_url, model_path, reporthook=report_progress)
    print("\n✓ Download completed!")
    
    # Move to models directory
    final_path = os.path.join('models', 'yolov8n.pt')
    shutil.move(model_path, final_path)
    print(f"✓ Model saved to {final_path}")
    
    # Verify the model exists
    if os.path.exists(final_path):
        print(f"✓ Verified: Model file exists at {final_path}")
        print(f"✓ File size: {os.path.getsize(final_path) / (1024*1024):.2f} MB")
    else:
        print("✗ Error: Model file not found after download")
        
except Exception as e:
    print(f"\n✗ Download failed: {e}")
    print("\nAlternative download methods:")
    print("1. Download manually from: https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt")
    print("2. Place the downloaded file in the 'models' directory")
    print("3. Or use wget/curl command:")
    print("   wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt -O models/yolov8n.pt")

print("\nDownload process completed.")

